import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/constants/app_constants.dart';
import 'core/network/api_client.dart';
import 'core/utils/app_theme.dart';
import 'core/utils/app_router.dart';
import 'core/di/service_locator.dart';
import 'shared/services/auth_service.dart';
import 'shared/services/storage_service.dart';
import 'shared/services/notification_service.dart';
import 'shared/services/document_service.dart';
import 'shared/services/invoice_service.dart';
import 'shared/services/contact_config_service.dart';
import 'features/auth/bloc/auth_bloc.dart';
import 'features/documents/bloc/document_bloc.dart';
import 'features/loads/bloc/load_bloc.dart';
import 'features/invoices/bloc/invoice_bloc.dart';
import 'shared/services/load_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (with error handling for missing configuration)
  try {
    await Firebase.initializeApp();
    if (kDebugMode) {
      print('Firebase initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Firebase initialization failed: $e');
      print('Continuing without Firebase features...');
    }
  }

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Initialize services
  await StorageService.init();

  // Initialize GetIt service locator
  await ServiceLocator.init();

  // Initialize notification service (with Firebase error handling)
  try {
    await NotificationService.init();
  } catch (e) {
    if (kDebugMode) {
      print('Notification service initialization failed: $e');
      print('Continuing without push notifications...');
    }
  }

  // Initialize contact configuration service
  try {
    await ContactConfigService.instance.initialize();
  } catch (e) {
    if (kDebugMode) {
      print('Contact config service initialization failed: $e');
      print('Continuing with default contact configuration...');
    }
  }

  runApp(const LogiPoolApp());
}

class LogiPoolApp extends StatelessWidget {
  const LogiPoolApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Services
        Provider<ApiClient>(
          create: (_) => ApiClient(baseUrl: AppConstants.baseUrl),
        ),
        ChangeNotifierProvider<AuthService>(
          create: (context) => AuthService(
            apiClient: context.read<ApiClient>(),
            storageService: StorageService(),
          ),
        ),
        Provider<StorageService>(
          create: (_) => StorageService(),
        ),
        Provider<NotificationService>(
          create: (_) => NotificationService(),
        ),
        ChangeNotifierProvider<ContactConfigService>(
          create: (context) => ContactConfigService(
            apiClient: context.read<ApiClient>(),
          ),
        ),
        Provider<DocumentService>(
          create: (context) => DocumentService(
            context.read<ApiClient>(),
          ),
        ),
        ChangeNotifierProvider<LoadService>(
          create: (context) => LoadService(
            context.read<ApiClient>(),
          ),
        ),
        Provider<InvoiceService>(
          create: (context) => InvoiceService(
            context.read<ApiClient>(),
          ),
        ),

        // BLoCs
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(
            authService: context.read<AuthService>(),
          ),
        ),
        BlocProvider<DocumentBloc>(
          create: (context) => DocumentBloc(
            documentService: context.read<DocumentService>(),
          ),
        ),
        BlocProvider<LoadBloc>(
          create: (context) => LoadBloc(
            context.read<LoadService>(),
          ),
        ),
        BlocProvider<InvoiceBloc>(
          create: (context) => InvoiceBloc(
            invoiceService: context.read<InvoiceService>(),
          ),
        ),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          return MaterialApp.router(
            title: 'LogiPool',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'), // English
            ],
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1.0),
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
