import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/admin_model.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/admin_bloc.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/recent_activities_card.dart';
import '../widgets/system_health_card.dart';
import '../widgets/quick_actions_card.dart';
import 'admin_users_screen.dart';
import 'admin_companies_screen.dart';
import 'admin_loads_screen.dart';
import 'admin_analytics_screen.dart';
import 'admin_settings_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AdminBloc>().add(const LoadDashboard());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Admin Dashboard',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<AdminBloc>().add(const LoadDashboard()),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'analytics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('Analytics'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'backup',
                child: Row(
                  children: [
                    Icon(Icons.backup),
                    SizedBox(width: 8),
                    Text('System Backup'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Logout', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<AdminBloc, AdminState>(
        listener: (context, state) {
          if (state is AdminError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is AdminOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          if (state is AdminLoading) {
            return const LoadingWidget();
          } else if (state is AdminError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () => context.read<AdminBloc>().add(const LoadDashboard()),
            );
          } else if (state is DashboardLoaded) {
            return _buildDashboardContent(state.dashboard);
          }
          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildDashboardContent(DashboardOverview dashboard) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminBloc>().add(const LoadDashboard());
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(),
            const SizedBox(height: 24),
            _buildStatsGrid(dashboard),
            const SizedBox(height: 24),
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildSystemHealth(dashboard.systemHealth),
            const SizedBox(height: 24),
            _buildRecentActivities(dashboard.recentActivities),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const CircleAvatar(
              radius: 30,
              backgroundColor: Colors.blue,
              child: Icon(Icons.admin_panel_settings, color: Colors.white, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, Admin',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'LogiPool Administration Dashboard',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Last updated: ${DateTime.now().toString().substring(0, 16)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(DashboardOverview dashboard) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        DashboardStatsCard(
          title: 'Total Users',
          value: dashboard.totalUsers?.toString() ?? '0',
          icon: Icons.people,
          color: Colors.blue,
          onTap: () => _navigateToUsers(),
        ),
        DashboardStatsCard(
          title: 'Total Companies',
          value: dashboard.totalCompanies?.toString() ?? '0',
          icon: Icons.business,
          color: Colors.green,
          onTap: () => _navigateToCompanies(),
        ),
        DashboardStatsCard(
          title: 'Active Loads',
          value: dashboard.activeLoads?.toString() ?? '0',
          icon: Icons.local_shipping,
          color: Colors.orange,
          onTap: () => _navigateToLoads(),
        ),
        DashboardStatsCard(
          title: 'Total Revenue',
          value: '\$${dashboard.totalRevenue?.toStringAsFixed(2) ?? '0.00'}',
          icon: Icons.attach_money,
          color: Colors.purple,
          onTap: () => _navigateToAnalytics(),
        ),
        DashboardStatsCard(
          title: 'Pending Verifications',
          value: dashboard.pendingVerifications?.toString() ?? '0',
          icon: Icons.pending_actions,
          color: Colors.red,
          onTap: () => _navigateToCompanies(),
        ),
        DashboardStatsCard(
          title: 'Monthly Revenue',
          value: '\$${dashboard.monthlyRevenue?.toStringAsFixed(2) ?? '0.00'}',
          icon: Icons.trending_up,
          color: Colors.teal,
          onTap: () => _navigateToAnalytics(),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return QuickActionsCard(
      onUserManagement: () => _navigateToUsers(),
      onCompanyManagement: () => _navigateToCompanies(),
      onLoadManagement: () => _navigateToLoads(),
      onSystemSettings: () => _navigateToSettings(),
      onAnalytics: () => _navigateToAnalytics(),
      onBackup: () => _showBackupDialog(),
    );
  }

  Widget _buildSystemHealth(SystemHealth? systemHealth) {
    if (systemHealth == null) return const SizedBox.shrink();
    return SystemHealthCard(systemHealth: systemHealth);
  }

  Widget _buildRecentActivities(List<RecentActivity>? activities) {
    if (activities == null || activities.isEmpty) return const SizedBox.shrink();
    return RecentActivitiesCard(activities: activities);
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _navigateToSettings();
        break;
      case 'analytics':
        _navigateToAnalytics();
        break;
      case 'backup':
        _showBackupDialog();
        break;
      case 'logout':
        _showLogoutDialog();
        break;
    }
  }

  void _navigateToUsers() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<AdminBloc>(),
          child: const AdminUsersScreen(),
        ),
      ),
    );
  }

  void _navigateToCompanies() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<AdminBloc>(),
          child: const AdminCompaniesScreen(),
        ),
      ),
    );
  }

  void _navigateToLoads() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<AdminBloc>(),
          child: const AdminLoadsScreen(),
        ),
      ),
    );
  }

  void _navigateToAnalytics() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<AdminBloc>(),
          child: const AdminAnalyticsScreen(),
        ),
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<AdminBloc>(),
          child: const AdminSettingsScreen(),
        ),
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Backup'),
        content: const Text('Select backup type:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminBloc>().add(const InitiateSystemBackup(backupType: 'FULL'));
            },
            child: const Text('Full Backup'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminBloc>().add(const InitiateSystemBackup(backupType: 'INCREMENTAL'));
            },
            child: const Text('Incremental'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
